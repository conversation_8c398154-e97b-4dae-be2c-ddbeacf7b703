import axios from 'axios';
import { ApiR<PERSON>ponse, FolderContent, SearchResult } from '../types';
import { collectClientMetadata, ClientMetadata } from '../utils/metadata';

// Use relative URL for API calls to leverage proxy

const api = axios.create({
  baseURL: '/api/v1.0',
  timeout: 30000,
});

// Request interceptor
api.interceptors.request.use(
  (config) => {
    // Add auth token if available
    const token = localStorage.getItem('token');
    if (token) {
      config.headers.token = token;
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// Response interceptor
api.interceptors.response.use(
  (response) => {
    // Backend returns {code, data, message} structure
    return response.data;
  },
  (error) => {
    if (error.response?.status === 401) {
      // Handle unauthorized
      localStorage.removeItem('token');
      window.location.href = '/login';
    }

    // Handle error message format
    let errorMessage = 'An error occurred';
    const errorData = error.response?.data;

    if (errorData) {
      if (typeof errorData.message === 'string') {
        errorMessage = errorData.message;
      } else if (errorData.message && typeof errorData.message === 'object') {
        // Handle backend message format {head, body}
        if (errorData.message.body) {
          errorMessage = errorData.message.body;
        } else if (errorData.message.head) {
          errorMessage = errorData.message.head;
        }
      } else if (typeof errorData === 'string') {
        errorMessage = errorData;
      }
    } else if (error.message) {
      errorMessage = error.message;
    }

    return Promise.reject(new Error(errorMessage));
  }
);

const fileApi = {
  // Upload file
  uploadFile: async (
    file: File,
    parentId?: string,
    onProgress?: (progress: number, uploadedBytes?: number, uploadSpeed?: number) => void,
    uploadSource?: string,
    retryCount: number = 0
  ): Promise<ApiResponse> => {
    // Collect client-side metadata
    const metadata = await collectClientMetadata(uploadSource, retryCount);

    const formData = new FormData();
    formData.append('file', file);
    // Send original filename separately to avoid encoding issues
    formData.append('originalFileName', file.name);
    if (parentId) {
      formData.append('parentId', parentId);
    }

    // Add metadata to form data
    Object.entries(metadata).forEach(([key, value]) => {
      if (value !== undefined && value !== null) {
        formData.append(key, value.toString());
      }
    });

    // Track upload start time and previous progress for speed calculation
    let uploadStartTime = Date.now();
    let lastProgressTime = uploadStartTime;
    let lastUploadedBytes = 0;

    return api.post('/files/upload', formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
      onUploadProgress: (progressEvent) => {
        if (onProgress && progressEvent.total) {
          const progress = Math.round((progressEvent.loaded * 100) / progressEvent.total);
          const uploadedBytes = progressEvent.loaded;

          // Calculate upload speed (bytes per second) using recent progress
          const currentTime = Date.now();
          const timeDiff = (currentTime - lastProgressTime) / 1000; // seconds
          const bytesDiff = uploadedBytes - lastUploadedBytes;

          let uploadSpeed = 0;
          if (timeDiff > 0.1) { // Only calculate if enough time has passed (100ms)
            uploadSpeed = Math.round(bytesDiff / timeDiff);
            lastProgressTime = currentTime;
            lastUploadedBytes = uploadedBytes;
          } else if (uploadedBytes > 0) {
            // Fallback to average speed from start
            const totalTime = (currentTime - uploadStartTime) / 1000;
            uploadSpeed = totalTime > 0 ? Math.round(uploadedBytes / totalTime) : 0;
          }

          onProgress(progress, uploadedBytes, uploadSpeed);
        }
      },
    });
  },

  // Download file - call directly to backend API
  downloadFile: (fileId: string): string => {
    const token = localStorage.getItem('token');
    const baseURL = process.env.REACT_APP_API_URL || 'http://localhost:3001';
    return `${baseURL}/api/v1.0/files/download/${fileId}${token ? `?token=${token}` : ''}`;
  },

  // Preview file - call directly to backend API
  previewFile: (fileId: string): string => {
    const token = localStorage.getItem('token');
    const baseURL = process.env.REACT_APP_API_URL || 'http://localhost:3001';
    return `${baseURL}/api/v1.0/files/preview/${fileId}${token ? `?token=${token}` : ''}`;
  },

  // Get file metadata
  getFileMetadata: (fileId: string): Promise<ApiResponse> => {
    return api.get(`/files/metadata/${fileId}`);
  },

  // Browse folder content
  browseFolder: (folderId?: string): Promise<ApiResponse<FolderContent>> => {
    const url = folderId ? `/browse/${folderId}` : '/browse';
    return api.get(url);
  },

  // Create folder
  createFolder: (folderName: string, parentId?: string): Promise<ApiResponse> => {
    return api.post('/folders/create', {
      folderName,
      parentId: parentId || null,
    });
  },

  // Get folder tree
  getFolderTree: (excludeId?: string, excludeDescendants?: boolean): Promise<ApiResponse> => {
    const params: any = {};
    if (excludeId) {
      params.excludeId = excludeId;
      if (excludeDescendants) {
        params.excludeDescendants = 'true';
      }
    }
    return api.get('/folders/tree', { params });
  },

  // Rename item
  renameItem: (itemId: string, newName: string): Promise<ApiResponse> => {
    return api.put(`/items/${itemId}/rename`, {
      name: newName,
    });
  },

  // Delete item
  deleteItem: (itemId: string): Promise<ApiResponse> => {
    return api.delete(`/items/${itemId}`);
  },

  // Search
  search: (query: string): Promise<ApiResponse<SearchResult>> => {
    return api.get('/search', {
      params: { q: query },
    });
  },

  // Get file info
  getFileInfo: (fileId: string): Promise<ApiResponse> => {
    return api.get(`/files/info/${fileId}`);
  },

  // Storage management
  getStorageInfo: (): Promise<ApiResponse> => {
    return api.get('/user/storage');
  },

  syncStorageStats: (): Promise<ApiResponse> => {
    return api.post('/user/storage-sync');
  },

  // Move file or folder
  moveItem: (itemId: string, destinationFolderId: string | null): Promise<ApiResponse> => {
    return api.post(`/items/move/${itemId}`, { destinationFolderId });
  },

  // Bulk move files and folders
  bulkMoveItems: (itemIds: string[], destinationFolderId: string | null): Promise<ApiResponse> => {
    return api.post('/items/bulk-move', { itemIds, destinationFolderId });
  },
};

export { fileApi };
export default api;
